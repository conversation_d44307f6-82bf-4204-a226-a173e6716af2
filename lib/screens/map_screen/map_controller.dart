import 'dart:async';

import 'dart:ui';
import 'package:dio/dio.dart';
import 'package:ems/common/constants/general.dart';
import 'package:ems/common/utils/shared_preferences.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:google_polyline_algorithm/google_polyline_algorithm.dart';
import 'package:location/location.dart';

/// MapController handles all map-related functionality for the application
///
/// This controller manages:
/// - Location services and permissions
/// - Current device location tracking
/// - Google Maps controller and camera positioning
/// - Map markers, polylines, and circles
/// - Waypoints fetching and rendering
/// - Direct polyline drawing between waypoints
/// - Map type toggling
///
/// It uses GetX for reactive state management and the Google Maps Flutter plugin
/// for map rendering and interaction.
class MapController extends GetxController {
  final Location _location = Location();

  final dio = Dio();

  var currentLocation = Rxn<LatLng>();

  static const LatLng defaultLocation = LatLng(28.6139, 77.2090);

  final RxBool hasLocationPermission = false.obs;

  final RxBool locationServicesEnabled = false.obs;

  final RxBool shouldUpdateCamera = true.obs;

  final RxBool isMapLoaded = false.obs;

  Rxn<GoogleMapController> googleMapController = Rxn<GoogleMapController>();

  final RxSet<Circle> circles = <Circle>{}.obs;
  final RxSet<Marker> markers = <Marker>{}.obs;
  final RxSet<Polyline> polylines = <Polyline>{}.obs;

  final Rx<MapType> currentMapType = MapType.normal.obs;

  final List<List<dynamic>> waypoints = [];

  /// Updates the camera position on the map to focus on a specific location
  ///
  /// This method animates the camera to center on the provided position with a zoom level of 16.0.
  /// The camera will only be updated if:
  /// 1. A valid GoogleMapController exists
  /// 2. The shouldUpdateCamera flag is set to true
  ///
  /// @param position The LatLng coordinates to move the camera to
  Future<void> updateCameraPosition(LatLng position) async {
    final controller = googleMapController.value;
    if (controller != null && shouldUpdateCamera.value) {
      await controller.animateCamera(
        CameraUpdate.newLatLngZoom(position, 16.0),
      );
    }
  }

  /// Checks if location services are enabled on the device and requests them if not
  ///
  /// This method:
  /// 1. Checks if location services are currently enabled
  /// 2. If not enabled, attempts to request the user to enable them
  /// 3. Updates the locationServicesEnabled reactive variable with the result
  /// 4. Handles any exceptions that might occur during the process
  ///
  /// @return A boolean indicating whether location services are enabled (true) or not (false)
  Future<bool> checkLocationServices() async {
    try {
      bool serviceEnabled = await _location.serviceEnabled();
      if (!serviceEnabled) {
        try {
          serviceEnabled = await _location.requestService();
        } catch (e) {
          serviceEnabled = false;
        }
      }

      locationServicesEnabled.value = serviceEnabled;
      return serviceEnabled;
    } catch (e) {
      locationServicesEnabled.value = false;
      return false;
    }
  }

  /// Checks and requests location permission from the user if not already granted
  ///
  /// This method:
  /// 1. Checks the current location permission status
  /// 2. If permission is denied, attempts to request it from the user
  /// 3. Updates the hasLocationPermission reactive variable with the result
  /// 4. Handles any exceptions that might occur during the process
  ///
  /// Permission is considered granted if the status is either:
  /// - PermissionStatus.granted (full access)
  /// - PermissionStatus.grantedLimited (limited access)
  ///
  /// @return A boolean indicating whether location permission is granted (true) or not (false)
  Future<bool> checkLocationPermission() async {
    try {
      PermissionStatus permissionStatus = await _location.hasPermission();

      if (permissionStatus == PermissionStatus.denied ||
          permissionStatus == PermissionStatus.deniedForever) {
        try {
          permissionStatus = await _location.requestPermission();
        } catch (e) {
          permissionStatus = PermissionStatus.denied;
        }
      }

      bool isGranted =
          permissionStatus == PermissionStatus.granted ||
          permissionStatus == PermissionStatus.grantedLimited;

      hasLocationPermission.value = isGranted;
      return isGranted;
    } catch (e) {
      hasLocationPermission.value = false;
      return false;
    }
  }

  /// Retrieves the current device location and updates the map camera
  ///
  /// This method:
  /// 1. Gets the current location data from the device
  /// 2. Validates that both latitude and longitude are available
  /// 3. Updates the currentLocation reactive variable with the new position
  /// 4. Calls updateCameraPosition to center the map on the current location
  ///
  /// If location data is unavailable or incomplete, no action is taken
  Future<void> getCurrentLocation() async {
    final locationData = await _location.getLocation();

    if (locationData.latitude != null && locationData.longitude != null) {
      final position = LatLng(locationData.latitude!, locationData.longitude!);
      currentLocation.value = position;
      await updateCameraPosition(position);
    }
  }

  /// Orchestrates the complete location fetching process with necessary checks
  ///
  /// This method performs a series of steps in sequence:
  /// 1. Checks if location services are enabled, requesting them if needed
  /// 2. Verifies location permissions are granted, requesting them if needed
  /// 3. Sets location accuracy to high for better precision
  /// 4. Retrieves the current device location
  ///
  /// The process will stop early if either location services are disabled
  /// or location permissions are not granted
  Future<void> fetchDeviceLocation() async {
    bool servicesEnabled = await checkLocationServices();
    if (!servicesEnabled) {
      return;
    }

    bool permissionGranted = await checkLocationPermission();
    if (!permissionGranted) {
      return;
    }

    await _location.changeSettings(accuracy: LocationAccuracy.high);

    await getCurrentLocation();
  }

  /// Fetches waypoints data from the API for a specific project
  ///
  /// This method:
  /// 1. Retrieves the authentication token from storage
  /// 2. Makes an authenticated API request to get waypoints for the specified project
  /// 3. Processes the response data and updates the waypoints list
  /// 4. Handles different data structures that might be returned by the API
  ///
  /// The waypoints data structure is normalized to always be a list of lists,
  /// where each inner list represents a group of connected waypoints
  ///
  /// @param projectId The ID of the project to fetch waypoints for
  Future<void> fetchWaypointsFromApi(String projectId) async {
    final authToken = await TokenStorage.getToken();
    final url = '$baseUrl/api/projects/$projectId/waypoints';

    final response = await dio.get(
      url,
      options: Options(headers: {'Authorization': 'Bearer $authToken'}),
    );

    if (response.statusCode == 200 && response.data['success'] == true) {
      waypoints.clear();

      final rawData = response.data['waypoints'];
      if (rawData is List && rawData.isNotEmpty) {
        if (rawData.first is List) {
          waypoints.addAll(List<List<dynamic>>.from(rawData));
        } else {
          waypoints.add([rawData]);
        }
      }
    }
  }

  /// Creates a custom circular marker icon with the specified color
  ///
  /// This method uses Flutter's Canvas API to:
  /// 1. Create a circular dot with the specified color
  /// 2. Convert the drawing to a bitmap image
  /// 3. Return a BitmapDescriptor that can be used as a marker icon on Google Maps
  ///
  /// The marker is created as a small colored circle with a transparent background,
  /// which provides a cleaner look than the default markers.
  ///
  /// @param color The color to use for the dot marker
  /// @return A BitmapDescriptor object that can be used as a marker icon
  Future<BitmapDescriptor> createDotMarker(Color color) async {
    final double size = 40.0;
    final pictureRecorder = PictureRecorder();
    final canvas = Canvas(
      pictureRecorder,
      Rect.fromPoints(Offset(0, 0), Offset(size, size)),
    );

    final paint =
        Paint()
          ..color = color
          ..style = PaintingStyle.fill;

    final radius = size / 4;
    final center = Offset(size / 2, size / 2);
    canvas.drawCircle(center, radius, paint);

    final picture = pictureRecorder.endRecording();
    final img = await picture.toImage(size.toInt(), size.toInt());
    final byteData = await img.toByteData(format: ImageByteFormat.png);
    final uint8List = byteData!.buffer.asUint8List();

    return BitmapDescriptor.bytes(uint8List);
  }

  /// Fetches and decodes a route between two points using Google Directions API
  ///
  /// This method:
  /// 1. Constructs a URL for the Google Directions API with start and end coordinates
  /// 2. Makes an API request to get the route information
  /// 3. Extracts the encoded polyline from the response
  /// 4. Decodes the polyline into a list of LatLng coordinates
  ///
  /// The route is optimized for driving directions by default.
  /// If the API request fails or returns an error status, an empty list is returned.
  ///
  /// @param start The starting point coordinates
  /// @param end The destination point coordinates
  /// @return A List of LatLng coordinates representing the route
  Future<List<LatLng>> getRouteCoordinates(LatLng start, LatLng end) async {
    final url =
        'https://maps.googleapis.com/maps/api/directions/json?origin=${start.latitude},${start.longitude}&destination=${end.latitude},${end.longitude}&mode=driving&key=$googleApiKey';

    final response = await dio.get(url);
    final data = response.data;

    if (data['status'] == 'OK') {
      final route = data['routes'][0];
      final overviewPolyline = route['overview_polyline']['points'];

      final decodedPoints = decodePolyline(overviewPolyline);
      return decodedPoints
          .map((p) => LatLng(p[0].toDouble(), p[1].toDouble()))
          .toList();
    } else {
      return [];
    }
  }

  /// Toggles between normal and satellite map views
  ///
  /// This method switches the map type between:
  /// - MapType.normal (standard road map)
  /// - MapType.satellite (satellite imagery)
  ///
  /// The current map type is stored in the reactive currentMapType variable,
  /// which automatically updates the UI when changed.
  void toggleMapType() {
    currentMapType.value =
        currentMapType.value == MapType.normal
            ? MapType.satellite
            : MapType.normal;
  }

  /// Renders all waypoints and direct routes on the map based on the fetched data
  ///
  /// This method:
  /// 1. Creates markers for each waypoint with appropriate colors (red for start, green for others)
  /// 2. Creates direct polylines between consecutive waypoints with straight lines
  /// 3. Handles multiple groups of waypoints as separate routes
  /// 4. Updates the map's markers and polylines collections
  /// 5. Sets the initial map position to the first waypoint if available
  ///
  /// The method processes each waypoint group sequentially, creating a visual representation
  /// of the routes with custom markers and connecting lines. Start points are marked in red,
  /// while other points are marked in green.
  ///
  /// Polylines are drawn as direct straight lines between waypoints for simplicity and efficiency.
  void renderWaypointsOnMap() async {
    final tempMarkers = <Marker>{};
    final tempPolylines = <Polyline>{};

    int polylineCounter = 0;
    List<LatLng> currentRoutePoints = [];

    for (int groupIndex = 0; groupIndex < waypoints.length; groupIndex++) {
      final group = waypoints[groupIndex];
      currentRoutePoints.clear();

      for (int pointIndex = 0; pointIndex < group.length; pointIndex++) {
        final waypoint = group[pointIndex];
        final lat = waypoint['latitude'];
        final lng = waypoint['longitude'];
        final isStart = waypoint['isStart'] ?? false;
        final isEnd = waypoint['isEnd'] ?? false;

        if (lat == null || lng == null) continue;

        final position = LatLng(lat.toDouble(), lng.toDouble());

        final markerColor = isStart ? Colors.red : Colors.green;
        final customMarker = await createDotMarker(markerColor);

        tempMarkers.add(
          Marker(
            markerId: MarkerId('waypoint_${groupIndex}_$pointIndex'),
            position: position,
            icon: customMarker,
            anchor: const Offset(0.5, 0.5),
          ),
        );

        // Simply add each waypoint to the route points for direct lines
        currentRoutePoints.add(position);

        if (isEnd) {
          if (currentRoutePoints.length >= 2) {
            tempPolylines.add(
              Polyline(
                polylineId: PolylineId('polyline_$polylineCounter'),
                points: List<LatLng>.from(currentRoutePoints),
                color: const Color(0xFF4CAF50),
                width: 4,
              ),
            );
            polylineCounter++;
          }
          currentRoutePoints.clear();
        }
      }
    }

    markers.assignAll(tempMarkers);
    polylines.assignAll(tempPolylines);

    if (waypoints.isNotEmpty &&
        waypoints[0].isNotEmpty &&
        waypoints[0][0]['latitude'] != null &&
        waypoints[0][0]['longitude'] != null) {
      currentLocation.value = LatLng(
        waypoints[0][0]['latitude'].toDouble(),
        waypoints[0][0]['longitude'].toDouble(),
      );
    }
  }
}
