import 'dart:async';

import 'package:ems/common/constants/app_colors.dart';
import 'package:ems/common/constants/size.dart';
import 'package:ems/common/widgets/custom_app_bar.dart';
import 'package:ems/common/widgets/custom_circularprogressindicator.dart';
import 'package:ems/common/widgets/custom_round_button.dart';
import 'package:ems/screens/create_track_screen/create_track_controller.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';

class CreateTrackScreen extends StatefulWidget {
  const CreateTrackScreen({super.key});

  @override
  State<CreateTrackScreen> createState() => _CreateTrackScreenState();
}

class _CreateTrackScreenState extends State<CreateTrackScreen> {
  final Completer<GoogleMapController> _controller = Completer();
  final createTrackController = Get.put(CreateTrackController());

  @override
  void initState() {
    super.initState();

    // Initialize the controller and render waypoints
    _initializeController();
  }

  Future<void> _initializeController() async {
    try {
      // Fetch device location first
      await createTrackController.fetchDeviceLocation();

      // Wait for the controller to finish API initialization
      // This ensures API data is available before rendering
      while (createTrackController.isLoading.value) {
        await Future.delayed(const Duration(milliseconds: 100));
      }

      // Now render the waypoints on the map with API data available
      await createTrackController.renderWaypointsOnMap();
    } catch (error) {
      createTrackController.locationServicesFailed.value = true;
      await createTrackController.renderWaypointsOnMap();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.white,
      body: Stack(
        children: [
          Column(
            children: [
              Obx(() => CustomAppBar(
                title: createTrackController.routeName.value.isEmpty
                    ? 'Track Route'
                    : createTrackController.routeName.value,
                onPressed: () => Get.back(),
                backgroundColor: Colors.transparent,
                textColor: AppColors.black,
              )),
              Expanded(
                child: Center(
                  child: Stack(
                    children: [
                      Obx(() {
                        if (createTrackController.isLoading.value) {
                          return const Center(
                            child: CustomCircularProgressIndicator(),
                          );
                        }

                        final location = createTrackController.currentLocation.value;

                        if (location == null) {
                          return const Center(
                            child: CustomCircularProgressIndicator(),
                          );
                        }

                        return GoogleMap(
                          initialCameraPosition: CameraPosition(
                            target: location,
                            zoom: 16.0,
                          ),
                          myLocationButtonEnabled: false,
                          zoomControlsEnabled: false,
                          markers: createTrackController.markers,
                          polylines: createTrackController.polylines,
                          onMapCreated: (GoogleMapController controller) {
                            if (!_controller.isCompleted) {
                              _controller.complete(controller);

                              if (createTrackController.locationServicesFailed.value &&
                                  createTrackController.markers.isEmpty &&
                                  createTrackController.waypoints.isEmpty) {
                                createTrackController.renderWaypointsOnMap();
                              }
                            }
                          }
                        );
                      }),
                      Positioned(
                        top: MediaQuery.of(context).size.height * 0.02,
                        right: MediaQuery.of(context).size.width * 0.02,
                        child: Column(
                          children: [
                            1.5.ph,
                            Container(
                              decoration: BoxDecoration(
                                color: const Color(0xFF4A3C2F),
                                borderRadius: BorderRadius.circular(12),
                                boxShadow: [
                                  BoxShadow(
                                    color: Colors.black12,
                                    blurRadius: 6,
                                    offset: Offset(0, 2),
                                  ),
                                ],
                              ),
                              child: Column(
                                children: [
                                  roundButton(
                                    icon: Icons.add,
                                    onPressed: () async {
                                      final controller = await _controller.future;
                                      controller.animateCamera(CameraUpdate.zoomIn());
                                    },
                                  ),
                                  1.25.ph,
                                  roundButton(
                                    icon: Icons.remove,
                                    onPressed: () async {
                                      final controller = await _controller.future;
                                      controller.animateCamera(CameraUpdate.zoomOut());
                                    },
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}