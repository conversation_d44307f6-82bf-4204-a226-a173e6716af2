import 'dart:ui';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:location/location.dart';
import 'package:dio/dio.dart';
import 'package:google_polyline_algorithm/google_polyline_algorithm.dart';
import 'package:ems/common/constants/general.dart';
import 'package:ems/common/utils/shared_preferences.dart';

/// CreateTrackController manages the track creation and visualization functionality
///
/// This controller is responsible for:
/// - Handling device location services and permissions
/// - Fetching and managing waypoint data
/// - Creating and rendering map markers and polylines
/// - Drawing direct polylines between waypoints
/// - Creating custom map markers
/// - Managing the loading state during async operations
///
/// It uses GetX for reactive state management and the Google Maps Flutter plugin
/// for map rendering and interaction.
class CreateTrackController extends GetxController {
  final Location _location = Location();
  final dio = Dio();

  static const LatLng newDelhiLocation = LatLng(28.6139, 77.2090);

  final Rxn<LatLng> currentLocation = Rxn<LatLng>();
  final RxSet<Marker> markers = <Marker>{}.obs;
  final RxSet<Polyline> polylines = <Polyline>{}.obs;
  final RxList<Map<String, dynamic>> waypoints = <Map<String, dynamic>>[].obs;
  final RxString routeName = ''.obs;
  final RxList<List<Map<String, dynamic>>> allRoutes = <List<Map<String, dynamic>>>[].obs;

  final RxBool locationServicesFailed = false.obs;
  final RxBool isLoading = true.obs;

  late GoogleMapController mapController;

  /// Initializes the controller when it's first created
  ///
  /// This method:
  /// 1. Calls the parent class's onInit method
  /// 2. Checks if arguments were passed during navigation
  /// 3. If arguments exist, uses them; otherwise fetches from API
  /// 4. Initializes the controller's state with the provided data
  ///
  /// The arguments are expected to be a Map containing:
  /// - 'waypoints': List of waypoint data
  /// - 'routeName': String name of the route
  @override
  void onInit() {
    super.onInit();

    if (Get.arguments != null) {
      final args = Get.arguments as Map<String, dynamic>;
      waypoints.assignAll(args['waypoints'] ?? []);
      routeName.value = args['routeName'] ?? '';
    } else {
      // Fetch waypoints from API if no arguments provided
      fetchEmployeeWaypoints();
    }
  }

  /// Fetches employee waypoints from the API
  ///
  /// This method:
  /// 1. Retrieves the authentication token from storage
  /// 2. Makes an authenticated API request to get all employee waypoints
  /// 3. Processes the response to extract waypoint data with gpsDetails and poleDetails
  /// 4. Stores all routes in allRoutes for conditional marker rendering
  /// 5. Flattens all waypoints for backward compatibility
  Future<void> fetchEmployeeWaypoints() async {
    try {
      isLoading.value = true;

      final authToken = await TokenStorage.getToken();
      if (authToken == null) {
        isLoading.value = false;
        return;
      }

      final url = '$baseUrl/api/projects/employee-waypoints';
      final response = await dio.get(
        url,
        options: Options(headers: {'Authorization': 'Bearer $authToken'}),
      );

      if (response.statusCode == 200 && response.data['success'] == true) {
        final List<dynamic> projects = response.data['projects'];
        allRoutes.clear();
        waypoints.clear();

        for (var project in projects) {
          if (project['waypoints'] != null) {
            for (var route in project['waypoints']) {
              if (route != null && route.isNotEmpty) {
                // Store each route separately for conditional rendering
                final routeWaypoints = List<Map<String, dynamic>>.from(route);
                allRoutes.add(routeWaypoints);

                // Also add to flat waypoints list for backward compatibility
                waypoints.addAll(routeWaypoints);
              }
            }
          }
        }

        // Set route name from first waypoint if available
        if (waypoints.isNotEmpty &&
            waypoints.first['gpsDetails'] != null &&
            waypoints.first['gpsDetails'].isNotEmpty) {
          final gpsDetails = waypoints.first['gpsDetails'][0];
          routeName.value = gpsDetails['feederName'] ?? 'Track Route';
        } else {
          routeName.value = 'Track Route';
        }
      }
    } catch (e) {
      // Handle error silently or use proper logging
    } finally {
      isLoading.value = false;
    }
  }

  /// Fetches the current device location with proper error handling
  ///
  /// This method:
  /// 1. Sets the loading state to true
  /// 2. Checks if location services are enabled and requests them if not
  /// 3. Verifies location permissions and requests them if needed
  /// 4. Sets location accuracy to high for better precision
  /// 5. Attempts to get the current device location
  /// 6. Updates the currentLocation with the device position if successful
  /// 7. Falls back to a default location if any step fails
  /// 8. Updates the locationServicesFailed flag based on the outcome
  ///
  /// The method only updates currentLocation if waypoints are empty,
  /// as waypoints take precedence over the current device location.
  /// If any error occurs, it falls back to the default location (New Delhi).
  Future<void> fetchDeviceLocation() async {
    try {
      isLoading.value = true;

      bool serviceEnabled = await _location.serviceEnabled();
      if (!serviceEnabled) {
        serviceEnabled = await _location.requestService();
        if (!serviceEnabled) {
          locationServicesFailed.value = true;

          if (waypoints.isEmpty) {
            currentLocation.value = newDelhiLocation;
          }
          return;
        }
      }

      PermissionStatus permissionGranted = await _location.hasPermission();
      if (permissionGranted == PermissionStatus.denied) {
        permissionGranted = await _location.requestPermission();
        if (permissionGranted != PermissionStatus.granted) {
          locationServicesFailed.value = true;

          if (waypoints.isEmpty) {
            currentLocation.value = newDelhiLocation;
          }
          return;
        }
      }

      await _location.changeSettings(accuracy: LocationAccuracy.high);

      try {
        final locationData = await _location.getLocation();

        if (locationData.latitude == null || locationData.longitude == null) {
          locationServicesFailed.value = true;

          if (waypoints.isEmpty) {
            currentLocation.value = newDelhiLocation;
          }
          return;
        }

        final LatLng position = LatLng(
          locationData.latitude!,
          locationData.longitude!,
        );

        if (waypoints.isEmpty) {
          currentLocation.value = position;
        }

        locationServicesFailed.value = false;
      } catch (e) {
        locationServicesFailed.value = true;

        if (waypoints.isEmpty) {
          currentLocation.value = newDelhiLocation;
        }
      }
    } catch (e) {
      locationServicesFailed.value = true;

      if (waypoints.isEmpty) {
        currentLocation.value = newDelhiLocation;
      }
    }
  }

  /// Fetches and decodes a route between two points using Google Directions API
  ///
  /// This method:
  /// 1. Constructs a URL for the Google Directions API with start and end coordinates
  /// 2. Makes an API request to get the route information
  /// 3. Extracts the encoded polyline from the response
  /// 4. Decodes the polyline into a list of LatLng coordinates
  ///
  /// The route is optimized for driving directions by default.
  /// If the API request fails or returns an error status, an empty list is returned.
  ///
  /// @param start The starting point coordinates
  /// @param end The destination point coordinates
  /// @return A List of LatLng coordinates representing the route
  Future<List<LatLng>> getRouteCoordinates(LatLng start, LatLng end) async {
    final url =
        'https://maps.googleapis.com/maps/api/directions/json?origin=${start.latitude},${start.longitude}&destination=${end.latitude},${end.longitude}&mode=walking&key=$googleApiKey';

    final response = await dio.get(url);
    final data = response.data;

    if (data['status'] == 'OK') {
      final route = data['routes'][0];
      final overviewPolyline = route['overview_polyline']['points'];

      final decodedPoints = decodePolyline(overviewPolyline);
      return decodedPoints
          .map((p) => LatLng(p[0].toDouble(), p[1].toDouble()))
          .toList();
    } else {
      return [];
    }
  }

  /// Creates a custom circular marker icon with the specified color
  ///
  /// This method uses Flutter's Canvas API to:
  /// 1. Create a circular dot with the specified color
  /// 2. Convert the drawing to a bitmap image
  /// 3. Return a BitmapDescriptor that can be used as a marker icon on Google Maps
  ///
  /// The marker is created as a small colored circle with a transparent background,
  /// which provides a cleaner look than the default markers.
  ///
  /// @param color The color to use for the dot marker
  /// @return A BitmapDescriptor object that can be used as a marker icon
  Future<BitmapDescriptor> createDotMarker(Color color) async {
    final double size = 40.0;
    final pictureRecorder = PictureRecorder();
    final canvas = Canvas(
      pictureRecorder,
      Rect.fromPoints(Offset(0, 0), Offset(size, size)),
    );

    final paint =
        Paint()
          ..color = color
          ..style = PaintingStyle.fill;

    final radius = size / 4;
    final center = Offset(size / 2, size / 2);
    canvas.drawCircle(center, radius, paint);

    final picture = pictureRecorder.endRecording();
    final img = await picture.toImage(size.toInt(), size.toInt());
    final byteData = await img.toByteData(format: ImageByteFormat.png);
    final uint8List = byteData!.buffer.asUint8List();

    return BitmapDescriptor.bytes(uint8List);
  }

  /// Creates a custom triangle marker icon with the specified color
  Future<BitmapDescriptor> createTriangleMarker(Color color) async {
    final double size = 40.0;
    final pictureRecorder = PictureRecorder();
    final canvas = Canvas(
      pictureRecorder,
      Rect.fromPoints(Offset(0, 0), Offset(size, size)),
    );

    final paint = Paint()
      ..color = color
      ..style = PaintingStyle.fill;

    final path = Path();
    path.moveTo(size / 2, size / 4); // Top vertex
    path.lineTo(size / 4, 3 * size / 4); // Bottom left vertex
    path.lineTo(3 * size / 4, 3 * size / 4); // Bottom right vertex
    path.close();

    canvas.drawPath(path, paint);

    final picture = pictureRecorder.endRecording();
    final img = await picture.toImage(size.toInt(), size.toInt());
    final byteData = await img.toByteData(format: ImageByteFormat.png);
    final uint8List = byteData!.buffer.asUint8List();

    return BitmapDescriptor.bytes(uint8List);
  }

  /// Creates a custom rectangle marker icon with the specified color
  Future<BitmapDescriptor> createRectangleMarker(Color color) async {
    final double size = 40.0;
    final pictureRecorder = PictureRecorder();
    final canvas = Canvas(
      pictureRecorder,
      Rect.fromPoints(Offset(0, 0), Offset(size, size)),
    );

    final paint = Paint()
      ..color = color
      ..style = PaintingStyle.fill;

    final rect = Rect.fromLTWH(size / 4, size / 4, size / 2, size / 2);
    canvas.drawRect(rect, paint);

    final picture = pictureRecorder.endRecording();
    final img = await picture.toImage(size.toInt(), size.toInt());
    final byteData = await img.toByteData(format: ImageByteFormat.png);
    final uint8List = byteData!.buffer.asUint8List();

    return BitmapDescriptor.bytes(uint8List);
  }

  /// Renders waypoints and direct polylines on the map with conditional markers
  Future<void> renderWaypointsOnMap() async {
    try {
      isLoading.value = true;

      if (waypoints.isEmpty) {
        // Fallback for empty waypoints
        currentLocation.value = newDelhiLocation;
        final customMarker = await createDotMarker(Colors.blue);
        markers.assignAll({
          Marker(
            markerId: const MarkerId('fallback'),
            position: newDelhiLocation,
            icon: customMarker,
            infoWindow: const InfoWindow(
              title: 'No Data',
              snippet: 'No waypoints available',
            ),
          ),
        });
        polylines.clear();
        isLoading.value = false;
        return;
      }

      final tempMarkers = <Marker>{};
      final tempPolylines = <Polyline>{};
      int polylineCounter = 0;

      // Process routes separately if available, otherwise process flat waypoints
      if (allRoutes.isNotEmpty) {
        // Process each route separately for proper polyline rendering
        for (int routeIndex = 0; routeIndex < allRoutes.length; routeIndex++) {
          final route = allRoutes[routeIndex];
          final routePoints = <LatLng>[];

          // Determine route type and polyline color from first waypoint with gpsDetails
          Color polylineColor = Colors.green; // Default
          String routeType = '';

          for (final wp in route) {
            if (wp['gpsDetails'] != null && wp['gpsDetails'].isNotEmpty) {
              final gpsDetails = wp['gpsDetails'][0];
              routeType = gpsDetails['routeType'] ?? '';
              break;
            }
          }

          // Set polyline color based on route type
          if (routeType == 'New') {
            polylineColor = const Color(0xFFFB8500); // Orange
          } else if (routeType == 'Existing') {
            polylineColor = Colors.black;
          }

          // Process each waypoint in the route
          for (int i = 0; i < route.length; i++) {
            final wp = route[i];
            final lat = wp['latitude']?.toDouble();
            final lng = wp['longitude']?.toDouble();

            if (lat == null || lng == null) continue;

            final position = LatLng(lat, lng);
            routePoints.add(position);

            // Create marker based on conditions
            final marker = await _createConditionalMarker(wp, routeIndex, i);
            if (marker != null) {
              tempMarkers.add(marker);
            }
          }

          // Create polyline for this route
          if (routePoints.length >= 2) {
            tempPolylines.add(
              Polyline(
                polylineId: PolylineId('route_$polylineCounter'),
                points: routePoints,
                color: polylineColor,
                width: 4,
                patterns: routeType == 'Existing' ? [PatternItem.dot, PatternItem.gap(10)] : [],
              ),
            );
            polylineCounter++;
          }
        }
      } else {
        // Fallback: process flat waypoints list
        final routePoints = <LatLng>[];

        for (int i = 0; i < waypoints.length; i++) {
          final wp = waypoints[i];
          final lat = wp['latitude']?.toDouble();
          final lng = wp['longitude']?.toDouble();

          if (lat == null || lng == null) continue;

          final position = LatLng(lat, lng);
          routePoints.add(position);

          // Create marker based on conditions
          final marker = await _createConditionalMarker(wp, 0, i);
          if (marker != null) {
            tempMarkers.add(marker);
          }
        }

        // Create single polyline for all waypoints
        if (routePoints.length >= 2) {
          tempPolylines.add(
            Polyline(
              polylineId: const PolylineId('track_path'),
              points: routePoints,
              color: Colors.green,
              width: 4,
            ),
          );
        }
      }

      // Update markers and polylines
      markers.assignAll(tempMarkers);
      polylines.assignAll(tempPolylines);

      // Set initial camera position
      if (waypoints.isNotEmpty) {
        final firstWaypoint = waypoints.first;
        final lat = firstWaypoint['latitude']?.toDouble();
        final lng = firstWaypoint['longitude']?.toDouble();
        if (lat != null && lng != null) {
          currentLocation.value = LatLng(lat, lng);
        }
      }

      isLoading.value = false;
    } catch (e) {
      // Fallback for error cases
      currentLocation.value = newDelhiLocation;
      final customMarker = await createDotMarker(Colors.blue);
      markers.assignAll({
        Marker(
          markerId: const MarkerId('error_fallback'),
          position: newDelhiLocation,
          icon: customMarker,
          infoWindow: const InfoWindow(
            title: 'Error',
            snippet: 'Failed to load waypoints',
          ),
        ),
      });
      polylines.clear();
      isLoading.value = false;
    }
  }

  /// Creates conditional markers based on poleDetails and gpsDetails
  Future<Marker?> _createConditionalMarker(Map<String, dynamic> wp, int routeIndex, int waypointIndex) async {
    final lat = wp['latitude']?.toDouble();
    final lng = wp['longitude']?.toDouble();

    if (lat == null || lng == null) return null;

    final position = LatLng(lat, lng);
    BitmapDescriptor markerIcon;
    String snippet = '';

    // Check for pole details first (higher priority)
    final hasPoleDetails = wp['poleDetails'] != null &&
                         (wp['poleDetails'] is List) &&
                         wp['poleDetails'].isNotEmpty;

    // Check for GPS details
    final hasGpsDetails = wp['gpsDetails'] != null &&
                        (wp['gpsDetails'] is List) &&
                        wp['gpsDetails'].isNotEmpty;

    if (hasPoleDetails) {
      final poleDetails = wp['poleDetails'][0];
      final existingOrNew = poleDetails['existingOrNewProposed'] ?? '';
      final poleType = poleDetails['poleType'] ?? '';

      // Conditional markers based on pole details
      if (poleType == 'Double Pole Structure') {
        // Triangle markers for Double Pole Structure
        if (existingOrNew == 'New') {
          markerIcon = await createTriangleMarker(const Color(0xFFFB8500)); // Orange
          snippet = 'New Double Pole Structure';
        } else { // Existing
          markerIcon = await createTriangleMarker(Colors.black);
          snippet = 'Existing Double Pole Structure';
        }
      } else {
        // Circle markers for other pole types
        if (existingOrNew == 'New') {
          markerIcon = await createDotMarker(const Color(0xFFFB8500)); // Orange
          snippet = 'New Pole: $poleType';
        } else { // Existing
          markerIcon = await createDotMarker(Colors.black);
          snippet = 'Existing Pole: $poleType';
        }
      }
    } else if (hasGpsDetails) {
      final gpsDetails = wp['gpsDetails'][0];
      final transformerType = gpsDetails['transformerType'] ?? '';

      // Rectangle markers for transformer data
      if (transformerType == 'New') {
        markerIcon = await createRectangleMarker(const Color(0xFFFB8500)); // Orange
        snippet = 'New Transformer';
      } else if (transformerType == 'Existing') {
        markerIcon = await createRectangleMarker(Colors.black);
        snippet = 'Existing Transformer';
      } else {
        // Default marker for GPS points without transformer type
        markerIcon = await createDotMarker(Colors.blue);
        snippet = 'GPS Point';
      }
    } else {
      // Default marker for waypoints without specific details
      markerIcon = await createDotMarker(Colors.blue);
      snippet = 'Waypoint';
    }

    return Marker(
      markerId: MarkerId('waypoint_${routeIndex}_$waypointIndex'),
      position: position,
      icon: markerIcon,
      infoWindow: InfoWindow(
        title: 'Waypoint ${waypointIndex + 1}',
        snippet: snippet,
      ),
    );
  }
}
