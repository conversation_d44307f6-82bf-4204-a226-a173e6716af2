import 'dart:ui';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:location/location.dart';
import 'package:dio/dio.dart';
import 'package:google_polyline_algorithm/google_polyline_algorithm.dart';
import 'package:ems/common/constants/general.dart';

/// CreateTrackController manages the track creation and visualization functionality
///
/// This controller is responsible for:
/// - Handling device location services and permissions
/// - Fetching and managing waypoint data
/// - Creating and rendering map markers and polylines
/// - Drawing direct polylines between waypoints
/// - Creating custom map markers
/// - Managing the loading state during async operations
///
/// It uses GetX for reactive state management and the Google Maps Flutter plugin
/// for map rendering and interaction.
class CreateTrackController extends GetxController {
  final Location _location = Location();
  final dio = Dio();

  static const LatLng newDelhiLocation = LatLng(28.6139, 77.2090);

  final Rxn<LatLng> currentLocation = Rxn<LatLng>();
  final RxSet<Marker> markers = <Marker>{}.obs;
  final RxSet<Polyline> polylines = <Polyline>{}.obs;
  final RxList<Map<String, dynamic>> waypoints = <Map<String, dynamic>>[].obs;
  final RxString routeName = ''.obs;

  final RxBool locationServicesFailed = false.obs;
  final RxBool isLoading = true.obs;

  late GoogleMapController mapController;

  /// Initializes the controller when it's first created
  ///
  /// This method:
  /// 1. Calls the parent class's onInit method
  /// 2. Checks if arguments were passed during navigation
  /// 3. Extracts waypoints and route name from the arguments if available
  /// 4. Initializes the controller's state with the provided data
  ///
  /// The arguments are expected to be a Map containing:
  /// - 'waypoints': List of waypoint data
  /// - 'routeName': String name of the route
  @override
  void onInit() {
    super.onInit();

    if (Get.arguments != null) {
      final args = Get.arguments as Map<String, dynamic>;
      waypoints.assignAll(args['waypoints'] ?? []);
      routeName.value = args['routeName'] ?? '';
    }
  }

  /// Fetches the current device location with proper error handling
  ///
  /// This method:
  /// 1. Sets the loading state to true
  /// 2. Checks if location services are enabled and requests them if not
  /// 3. Verifies location permissions and requests them if needed
  /// 4. Sets location accuracy to high for better precision
  /// 5. Attempts to get the current device location
  /// 6. Updates the currentLocation with the device position if successful
  /// 7. Falls back to a default location if any step fails
  /// 8. Updates the locationServicesFailed flag based on the outcome
  ///
  /// The method only updates currentLocation if waypoints are empty,
  /// as waypoints take precedence over the current device location.
  /// If any error occurs, it falls back to the default location (New Delhi).
  Future<void> fetchDeviceLocation() async {
    try {
      isLoading.value = true;

      bool serviceEnabled = await _location.serviceEnabled();
      if (!serviceEnabled) {
        serviceEnabled = await _location.requestService();
        if (!serviceEnabled) {
          locationServicesFailed.value = true;

          if (waypoints.isEmpty) {
            currentLocation.value = newDelhiLocation;
          }
          return;
        }
      }

      PermissionStatus permissionGranted = await _location.hasPermission();
      if (permissionGranted == PermissionStatus.denied) {
        permissionGranted = await _location.requestPermission();
        if (permissionGranted != PermissionStatus.granted) {
          locationServicesFailed.value = true;

          if (waypoints.isEmpty) {
            currentLocation.value = newDelhiLocation;
          }
          return;
        }
      }

      await _location.changeSettings(accuracy: LocationAccuracy.high);

      try {
        final locationData = await _location.getLocation();

        if (locationData.latitude == null || locationData.longitude == null) {
          locationServicesFailed.value = true;

          if (waypoints.isEmpty) {
            currentLocation.value = newDelhiLocation;
          }
          return;
        }

        final LatLng position = LatLng(
          locationData.latitude!,
          locationData.longitude!,
        );

        if (waypoints.isEmpty) {
          currentLocation.value = position;
        }

        locationServicesFailed.value = false;
      } catch (e) {
        locationServicesFailed.value = true;

        if (waypoints.isEmpty) {
          currentLocation.value = newDelhiLocation;
        }
      }
    } catch (e) {
      locationServicesFailed.value = true;

      if (waypoints.isEmpty) {
        currentLocation.value = newDelhiLocation;
      }
    }
  }

  /// Fetches and decodes a route between two points using Google Directions API
  ///
  /// This method:
  /// 1. Constructs a URL for the Google Directions API with start and end coordinates
  /// 2. Makes an API request to get the route information
  /// 3. Extracts the encoded polyline from the response
  /// 4. Decodes the polyline into a list of LatLng coordinates
  ///
  /// The route is optimized for driving directions by default.
  /// If the API request fails or returns an error status, an empty list is returned.
  ///
  /// @param start The starting point coordinates
  /// @param end The destination point coordinates
  /// @return A List of LatLng coordinates representing the route
  Future<List<LatLng>> getRouteCoordinates(LatLng start, LatLng end) async {
    final url =
        'https://maps.googleapis.com/maps/api/directions/json?origin=${start.latitude},${start.longitude}&destination=${end.latitude},${end.longitude}&mode=walking&key=$googleApiKey';

    final response = await dio.get(url);
    final data = response.data;

    if (data['status'] == 'OK') {
      final route = data['routes'][0];
      final overviewPolyline = route['overview_polyline']['points'];

      final decodedPoints = decodePolyline(overviewPolyline);
      return decodedPoints
          .map((p) => LatLng(p[0].toDouble(), p[1].toDouble()))
          .toList();
    } else {
      return [];
    }
  }

  /// Creates a custom circular marker icon with the specified color
  ///
  /// This method uses Flutter's Canvas API to:
  /// 1. Create a circular dot with the specified color
  /// 2. Convert the drawing to a bitmap image
  /// 3. Return a BitmapDescriptor that can be used as a marker icon on Google Maps
  ///
  /// The marker is created as a small colored circle with a transparent background,
  /// which provides a cleaner look than the default markers.
  ///
  /// @param color The color to use for the dot marker
  /// @return A BitmapDescriptor object that can be used as a marker icon
  Future<BitmapDescriptor> createDotMarker(Color color) async {
    final double size = 40.0;
    final pictureRecorder = PictureRecorder();
    final canvas = Canvas(
      pictureRecorder,
      Rect.fromPoints(Offset(0, 0), Offset(size, size)),
    );

    final paint =
        Paint()
          ..color = color
          ..style = PaintingStyle.fill;

    final radius = size / 4;
    final center = Offset(size / 2, size / 2);
    canvas.drawCircle(center, radius, paint);

    final picture = pictureRecorder.endRecording();
    final img = await picture.toImage(size.toInt(), size.toInt());
    final byteData = await img.toByteData(format: ImageByteFormat.png);
    final uint8List = byteData!.buffer.asUint8List();

    return BitmapDescriptor.bytes(uint8List);
  }

  /// Creates a custom triangle marker icon with the specified color
  Future<BitmapDescriptor> createTriangleMarker(Color color) async {
    final double size = 40.0;
    final pictureRecorder = PictureRecorder();
    final canvas = Canvas(
      pictureRecorder,
      Rect.fromPoints(Offset(0, 0), Offset(size, size)),
    );

    final paint = Paint()
      ..color = color
      ..style = PaintingStyle.fill;

    final path = Path();
    path.moveTo(size / 2, size / 4); // Top vertex
    path.lineTo(size / 4, 3 * size / 4); // Bottom left vertex
    path.lineTo(3 * size / 4, 3 * size / 4); // Bottom right vertex
    path.close();

    canvas.drawPath(path, paint);

    final picture = pictureRecorder.endRecording();
    final img = await picture.toImage(size.toInt(), size.toInt());
    final byteData = await img.toByteData(format: ImageByteFormat.png);
    final uint8List = byteData!.buffer.asUint8List();

    return BitmapDescriptor.bytes(uint8List);
  }

  /// Creates a custom rectangle marker icon with the specified color
  Future<BitmapDescriptor> createRectangleMarker(Color color) async {
    final double size = 40.0;
    final pictureRecorder = PictureRecorder();
    final canvas = Canvas(
      pictureRecorder,
      Rect.fromPoints(Offset(0, 0), Offset(size, size)),
    );

    final paint = Paint()
      ..color = color
      ..style = PaintingStyle.fill;

    final rect = Rect.fromLTWH(size / 4, size / 4, size / 2, size / 2);
    canvas.drawRect(rect, paint);

    final picture = pictureRecorder.endRecording();
    final img = await picture.toImage(size.toInt(), size.toInt());
    final byteData = await img.toByteData(format: ImageByteFormat.png);
    final uint8List = byteData!.buffer.asUint8List();

    return BitmapDescriptor.bytes(uint8List);
  }

  /// Renders waypoints and direct polylines on the map with conditional markers
  Future<void> renderWaypointsOnMap() async {
    try {
      isLoading.value = true;

      if (waypoints.isEmpty) {
        // Existing fallback code for empty waypoints
        if (currentLocation.value == null) {
          currentLocation.value = newDelhiLocation;
          final customMarker = await createDotMarker(Colors.blue);
          markers.assignAll({
            Marker(
              markerId: const MarkerId('new_delhi'),
              position: newDelhiLocation,
              icon: customMarker,
              infoWindow: const InfoWindow(
                title: 'New Delhi',
                snippet: 'Fallback Location',
              ),
            ),
          });
        }
        isLoading.value = false;
        return;
      }

      final tempMarkers = <Marker>{};
      final tempPolylinePoints = <LatLng>[];

      // Extract valid waypoint positions
      final waypointPositions = <LatLng>[];
      for (final wp in waypoints) {
        final lat = wp['latitude']?.toDouble();
        final lng = wp['longitude']?.toDouble();
        if (lat != null && lng != null) {
          waypointPositions.add(LatLng(lat, lng));
        }
      }

      if (waypointPositions.isNotEmpty) {
        currentLocation.value = waypointPositions.first;
      } else {
        // Fallback for empty positions
        currentLocation.value = newDelhiLocation;
        final customMarker = await createDotMarker(Colors.blue);
        markers.assignAll({
          Marker(
            markerId: const MarkerId('new_delhi'),
            position: newDelhiLocation,
            icon: customMarker,
            infoWindow: const InfoWindow(
              title: 'New Delhi',
              snippet: 'Fallback Location',
            ),
          ),
        });
        isLoading.value = false;
        return;
      }

      // Process each waypoint
      for (int i = 0; i < waypointPositions.length; i++) {
        final position = waypointPositions[i];
        final wp = waypoints[i];
        
        BitmapDescriptor markerIcon;
        String snippet = '';
        
        // Check if this is a start or end point
        if (wp['isStart'] == true) {
          markerIcon = await createDotMarker(Colors.red);
          snippet = 'Start Point';
        } else if (wp['isEnd'] == true) {
          markerIcon = await createDotMarker(Colors.green);
          snippet = 'End Point';
        } else {
          // Check for pole details
          final hasPoleDetails = wp['poleDetails'] != null && 
                               (wp['poleDetails'] is List) && 
                               wp['poleDetails'].isNotEmpty;
          
          // Check for GPS details
          final hasGpsDetails = wp['gpsDetails'] != null && 
                              (wp['gpsDetails'] is List) && 
                              wp['gpsDetails'].isNotEmpty;
          
          if (hasPoleDetails) {
            final poleDetails = wp['poleDetails'][0];
            final existingOrNew = poleDetails['existingOrNewProposed'] ?? '';
            final poleType = poleDetails['poleType'] ?? '';
            
            // Determine marker type and color based on pole details
            if (poleType == 'Double Pole Structure') {
              // Triangle marker
              if (existingOrNew == 'New') {
                markerIcon = await createTriangleMarker(Colors.orange);
                snippet = 'New Double Pole Structure';
              } else { // Existing
                markerIcon = await createTriangleMarker(Colors.black);
                snippet = 'Existing Double Pole Structure';
              }
            } else {
              // Circle marker
              if (existingOrNew == 'New') {
                markerIcon = await createDotMarker(Colors.orange);
                snippet = 'New Pole: $poleType';
              } else { // Existing
                markerIcon = await createDotMarker(Colors.black);
                snippet = 'Existing Pole: $poleType';
              }
            }
          } else if (hasGpsDetails) {
            final gpsDetails = wp['gpsDetails'][0];
            final transformerType = gpsDetails['transformerType'] ?? '';
            
            // Determine marker type based on transformer type
            if (transformerType == 'New') {
              markerIcon = await createRectangleMarker(Colors.orange);
              snippet = 'New Transformer';
            } else if (transformerType == 'Existing') {
              markerIcon = await createRectangleMarker(Colors.black);
              snippet = 'Existing Transformer';
            } else {
              // Default marker for GPS points without transformer type
              markerIcon = await createDotMarker(Colors.blue);
              snippet = 'GPS Point';
            }
          } else {
            // Default marker for other waypoints
            markerIcon = await createDotMarker(Colors.blue);
            snippet = 'Waypoint';
          }
        }

        // Create marker with appropriate icon and info
        tempMarkers.add(
          Marker(
            markerId: MarkerId('waypoint_$i'),
            position: position,
            icon: markerIcon,
            infoWindow: InfoWindow(
              title: wp['name'] ?? 'Waypoint ${i + 1}',
              snippet: snippet,
            ),
          ),
        );

        // Add each waypoint to the polyline points
        tempPolylinePoints.add(position);
      }

      // Update markers and polylines
      markers.assignAll(tempMarkers);

      if (tempPolylinePoints.isNotEmpty) {
        polylines.assignAll({
          Polyline(
            polylineId: const PolylineId('track_path'),
            points: tempPolylinePoints,
            color: Colors.green,
            width: 4,
          ),
        });
      } else {
        polylines.clear();
      }
      
      isLoading.value = false;
    } catch (e) {
      // Fallback for error cases
      if (waypoints.isEmpty) {
        currentLocation.value = newDelhiLocation;
        final customMarker = await createDotMarker(Colors.blue);
        markers.assignAll({
          Marker(
            markerId: const MarkerId('new_delhi'),
            position: newDelhiLocation,
            icon: customMarker,
            infoWindow: const InfoWindow(
              title: 'New Delhi',
              snippet: 'Fallback Location',
            ),
          ),
        });
        polylines.clear();
      }
      isLoading.value = false;
    }
  }
}
