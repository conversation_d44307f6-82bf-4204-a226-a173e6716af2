import 'dart:ui';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:location/location.dart';
import 'package:dio/dio.dart';
import 'package:google_polyline_algorithm/google_polyline_algorithm.dart';
import 'package:ems/common/constants/general.dart';
import 'package:ems/common/constants/app_colors.dart';

/// CreateTrackController manages the track creation and visualization functionality
///
/// This controller is responsible for:
/// - Handling device location services and permissions
/// - Fetching and managing waypoint data
/// - Creating and rendering map markers and polylines
/// - Drawing direct polylines between waypoints
/// - Creating custom map markers
/// - Managing the loading state during async operations
///
/// It uses GetX for reactive state management and the Google Maps Flutter plugin
/// for map rendering and interaction.
class CreateTrackController extends GetxController {
  final Location _location = Location();
  final dio = Dio();

  static const LatLng newDelhiLocation = LatLng(28.6139, 77.2090);

  final Rxn<LatLng> currentLocation = Rxn<LatLng>();
  final RxSet<Marker> markers = <Marker>{}.obs;
  final RxSet<Polyline> polylines = <Polyline>{}.obs;
  final RxList<Map<String, dynamic>> waypoints = <Map<String, dynamic>>[].obs;
  final RxString routeName = ''.obs;

  final RxBool locationServicesFailed = false.obs;
  final RxBool isLoading = true.obs;

  late GoogleMapController mapController;

  /// Initializes the controller when it's first created
  ///
  /// This method:
  /// 1. Calls the parent class's onInit method
  /// 2. Checks if arguments were passed during navigation
  /// 3. Extracts waypoints and route name from the arguments if available
  /// 4. Initializes the controller's state with the provided data
  ///
  /// The arguments are expected to be a Map containing:
  /// - 'waypoints': List of waypoint data
  /// - 'routeName': String name of the route
  @override
  void onInit() {
    super.onInit();

    if (Get.arguments != null) {
      final args = Get.arguments as Map<String, dynamic>;
      waypoints.assignAll(args['waypoints'] ?? []);
      routeName.value = args['routeName'] ?? '';
    }
  }

  /// Fetches the current device location with proper error handling
  ///
  /// This method:
  /// 1. Sets the loading state to true
  /// 2. Checks if location services are enabled and requests them if not
  /// 3. Verifies location permissions and requests them if needed
  /// 4. Sets location accuracy to high for better precision
  /// 5. Attempts to get the current device location
  /// 6. Updates the currentLocation with the device position if successful
  /// 7. Falls back to a default location if any step fails
  /// 8. Updates the locationServicesFailed flag based on the outcome
  ///
  /// The method only updates currentLocation if waypoints are empty,
  /// as waypoints take precedence over the current device location.
  /// If any error occurs, it falls back to the default location (New Delhi).
  Future<void> fetchDeviceLocation() async {
    try {
      isLoading.value = true;

      bool serviceEnabled = await _location.serviceEnabled();
      if (!serviceEnabled) {
        serviceEnabled = await _location.requestService();
        if (!serviceEnabled) {
          locationServicesFailed.value = true;

          if (waypoints.isEmpty) {
            currentLocation.value = newDelhiLocation;
          }
          return;
        }
      }

      PermissionStatus permissionGranted = await _location.hasPermission();
      if (permissionGranted == PermissionStatus.denied) {
        permissionGranted = await _location.requestPermission();
        if (permissionGranted != PermissionStatus.granted) {
          locationServicesFailed.value = true;

          if (waypoints.isEmpty) {
            currentLocation.value = newDelhiLocation;
          }
          return;
        }
      }

      await _location.changeSettings(accuracy: LocationAccuracy.high);

      try {
        final locationData = await _location.getLocation();

        if (locationData.latitude == null || locationData.longitude == null) {
          locationServicesFailed.value = true;

          if (waypoints.isEmpty) {
            currentLocation.value = newDelhiLocation;
          }
          return;
        }

        final LatLng position = LatLng(
          locationData.latitude!,
          locationData.longitude!,
        );

        if (waypoints.isEmpty) {
          currentLocation.value = position;
        }

        locationServicesFailed.value = false;
      } catch (e) {
        locationServicesFailed.value = true;

        if (waypoints.isEmpty) {
          currentLocation.value = newDelhiLocation;
        }
      }
    } catch (e) {
      locationServicesFailed.value = true;

      if (waypoints.isEmpty) {
        currentLocation.value = newDelhiLocation;
      }
    }
  }

  /// Fetches and decodes a route between two points using Google Directions API
  ///
  /// This method:
  /// 1. Constructs a URL for the Google Directions API with start and end coordinates
  /// 2. Makes an API request to get the route information
  /// 3. Extracts the encoded polyline from the response
  /// 4. Decodes the polyline into a list of LatLng coordinates
  ///
  /// The route is optimized for driving directions by default.
  /// If the API request fails or returns an error status, an empty list is returned.
  ///
  /// @param start The starting point coordinates
  /// @param end The destination point coordinates
  /// @return A List of LatLng coordinates representing the route
  Future<List<LatLng>> getRouteCoordinates(LatLng start, LatLng end) async {
    final url =
        'https://maps.googleapis.com/maps/api/directions/json?origin=${start.latitude},${start.longitude}&destination=${end.latitude},${end.longitude}&mode=walking&key=$googleApiKey';

    final response = await dio.get(url);
    final data = response.data;

    if (data['status'] == 'OK') {
      final route = data['routes'][0];
      final overviewPolyline = route['overview_polyline']['points'];

      final decodedPoints = decodePolyline(overviewPolyline);
      return decodedPoints
          .map((p) => LatLng(p[0].toDouble(), p[1].toDouble()))
          .toList();
    } else {
      return [];
    }
  }

  /// Creates a custom circular marker icon with the specified color
  ///
  /// This method uses Flutter's Canvas API to:
  /// 1. Create a circular dot with the specified color
  /// 2. Convert the drawing to a bitmap image
  /// 3. Return a BitmapDescriptor that can be used as a marker icon on Google Maps
  ///
  /// The marker is created as a small colored circle with a transparent background,
  /// which provides a cleaner look than the default markers.
  ///
  /// @param color The color to use for the dot marker
  /// @return A BitmapDescriptor object that can be used as a marker icon
  Future<BitmapDescriptor> createDotMarker(Color color) async {
    final double size = 40.0;
    final pictureRecorder = PictureRecorder();
    final canvas = Canvas(
      pictureRecorder,
      Rect.fromPoints(Offset(0, 0), Offset(size, size)),
    );

    final paint =
        Paint()
          ..color = color
          ..style = PaintingStyle.fill;

    final radius = size / 4;
    final center = Offset(size / 2, size / 2);
    canvas.drawCircle(center, radius, paint);

    final picture = pictureRecorder.endRecording();
    final img = await picture.toImage(size.toInt(), size.toInt());
    final byteData = await img.toByteData(format: ImageByteFormat.png);
    final uint8List = byteData!.buffer.asUint8List();

    return BitmapDescriptor.bytes(uint8List);
  }

  /// Creates a custom triangular marker icon with the specified color
  ///
  /// This method uses Flutter's Canvas API to:
  /// 1. Create a triangular shape with the specified color
  /// 2. Convert the drawing to a bitmap image
  /// 3. Return a BitmapDescriptor that can be used as a marker icon on Google Maps
  ///
  /// The marker is created as a small colored triangle with a transparent background,
  /// which provides a visual distinction for Double Pole Structure waypoints.
  ///
  /// @param color The color to use for the triangle marker
  /// @return A BitmapDescriptor object that can be used as a marker icon
  Future<BitmapDescriptor> createTriangleMarker(Color color) async {
    final double size = 40.0;
    final pictureRecorder = PictureRecorder();
    final canvas = Canvas(
      pictureRecorder,
      Rect.fromPoints(Offset(0, 0), Offset(size, size)),
    );

    final paint = Paint()
      ..color = color
      ..style = PaintingStyle.fill;

    final path = Path();
    // Draw a triangle pointing upward
    path.moveTo(size / 2, size / 4); // Top point
    path.lineTo(size / 4, 3 * size / 4); // Bottom left
    path.lineTo(3 * size / 4, 3 * size / 4); // Bottom right
    path.close();

    canvas.drawPath(path, paint);

    final picture = pictureRecorder.endRecording();
    final img = await picture.toImage(size.toInt(), size.toInt());
    final byteData = await img.toByteData(format: ImageByteFormat.png);
    final uint8List = byteData!.buffer.asUint8List();

    return BitmapDescriptor.bytes(uint8List);
  }

  /// Creates a custom rectangular marker icon with the specified color
  ///
  /// This method uses Flutter's Canvas API to:
  /// 1. Create a rectangular shape with the specified color
  /// 2. Convert the drawing to a bitmap image
  /// 3. Return a BitmapDescriptor that can be used as a marker icon on Google Maps
  ///
  /// The marker is created as a small colored rectangle with a transparent background,
  /// which provides a visual distinction for waypoints with transformer information.
  ///
  /// @param color The color to use for the rectangle marker
  /// @return A BitmapDescriptor object that can be used as a marker icon
  Future<BitmapDescriptor> createRectangleMarker(Color color) async {
    final double size = 40.0;
    final pictureRecorder = PictureRecorder();
    final canvas = Canvas(
      pictureRecorder,
      Rect.fromPoints(Offset(0, 0), Offset(size, size)),
    );

    final paint = Paint()
      ..color = color
      ..style = PaintingStyle.fill;

    // Draw a rectangle in the center of the canvas
    final rect = Rect.fromCenter(
      center: Offset(size / 2, size / 2),
      width: size / 2,
      height: size / 3,
    );
    canvas.drawRect(rect, paint);

    final picture = pictureRecorder.endRecording();
    final img = await picture.toImage(size.toInt(), size.toInt());
    final byteData = await img.toByteData(format: ImageByteFormat.png);
    final uint8List = byteData!.buffer.asUint8List();

    return BitmapDescriptor.bytes(uint8List);
  }

  /// Renders waypoints and direct polylines on the map
  ///
  /// This comprehensive method:
  /// 1. Sets the loading state to true during the rendering process
  /// 2. Handles the case when no waypoints are available by showing a fallback location
  /// 3. Extracts valid waypoint positions from the waypoints data
  /// 4. Creates custom markers for each waypoint with appropriate colors and shapes based on poleDetails and gpsDetails
  /// 5. Creates a direct polyline connecting all waypoints with style based on routeType
  /// 6. Updates the map's markers and polylines collections
  /// 7. Sets the map's initial position to the first waypoint or fallback location
  /// 8. Handles any errors that might occur during the process
  /// 9. Sets the loading state to false when complete
  ///
  /// Marker colors and shapes are determined by the following rules:
  /// - Colors:
  ///   - If poleDetails exists with 'existingOrNewProposed' = 'Existing': Black marker
  ///   - If poleDetails exists with 'existingOrNewProposed' = 'New': Orange marker (#FB8500)
  ///   - If poleDetails is empty but gpsDetails has 'transformerType' = 'Existing': Black marker
  ///   - If poleDetails is empty but gpsDetails has 'transformerType' = 'New': Orange marker (#FB8500)
  ///   - Otherwise: Red for start points, green for other points
  /// - Shapes:
  ///   - If poleDetails exists with 'poleType' = 'Double Pole Structure': Triangle marker
  ///   - If poleDetails is empty but gpsDetails has transformerType: Rectangle marker
  ///   - Otherwise: Circle marker
  ///
  /// Polyline styles are determined by the routeType:
  /// - 'New' routes: Orange solid line (#FB8500)
  /// - 'Existing' routes: Black dotted line
  Future<void> renderWaypointsOnMap() async {
    try {
      isLoading.value = true;

      if (waypoints.isEmpty) {
        if (currentLocation.value == null) {
          currentLocation.value = newDelhiLocation;

          final customMarker = await createDotMarker(Colors.blue);
          markers.assignAll({
            Marker(
              markerId: const MarkerId('new_delhi'),
              position: newDelhiLocation,
              icon: customMarker,
              infoWindow: const InfoWindow(
                title: 'New Delhi',
                snippet: 'Fallback Location',
              ),
            ),
          });
        }

        isLoading.value = false;
        return;
      }

      final tempMarkers = <Marker>{};
      final tempPolylinePoints = <LatLng>[];

      final waypointPositions = <LatLng>[];
      for (final wp in waypoints) {
        final lat = wp['latitude']?.toDouble();
        final lng = wp['longitude']?.toDouble();
        if (lat != null && lng != null) {
          waypointPositions.add(LatLng(lat, lng));
        }
      }

      if (waypointPositions.isNotEmpty) {
        currentLocation.value = waypointPositions.first;
      }

      if (waypointPositions.isEmpty) {
        currentLocation.value = newDelhiLocation;

        final customMarker = await createDotMarker(Colors.blue);
        markers.assignAll({
          Marker(
            markerId: const MarkerId('new_delhi'),
            position: newDelhiLocation,
            icon: customMarker,
            infoWindow: const InfoWindow(
              title: 'New Delhi',
              snippet: 'Fallback Location',
            ),
          ),
        });

        isLoading.value = false;
        return;
      }

      // Get the routeType from the first waypoint (if available)
      String routeType = 'Existing'; // Default value
      if (waypoints.isNotEmpty && waypoints[0]['routeType'] != null) {
        routeType = waypoints[0]['routeType'];
      }

      for (int i = 0; i < waypointPositions.length; i++) {
        final position = waypointPositions[i];
        final wp = waypoints[i];
        final isStart = wp['isStart'] ?? false;

        // Determine marker color and shape based on poleDetails or gpsDetails
        Color markerColor;
        bool isDoublePoleStructure = false;
        bool hasTransformerInfo = false;
        String transformerType = '';

        // Check if poleDetails exists and is not empty
        final poleDetails = wp['poleDetails'];
        if (poleDetails != null && poleDetails is List && poleDetails.isNotEmpty) {
          // Get the first poleDetail and check existingOrNewProposed field
          final firstPoleDetail = poleDetails.first;
          final existingOrNewProposed = firstPoleDetail['existingOrNewProposed'];
          final poleType = firstPoleDetail['poleType'];

          // Check if this is a Double Pole Structure
          isDoublePoleStructure = poleType == 'Double Pole Structure';

          if (existingOrNewProposed == 'Existing') {
            markerColor = Colors.black;
          } else if (existingOrNewProposed == 'New') {
            markerColor = AppColors.primaryOrange;
          } else {
            // Default color logic if existingOrNewProposed has unexpected value
            markerColor = isStart ? Colors.red : Colors.green;
          }
        } else {
          // If poleDetails is empty, check gpsDetails for transformerType
          final gpsDetails = wp['gpsDetails'];
          if (gpsDetails != null && gpsDetails is List && gpsDetails.isNotEmpty) {
            final firstGpsDetail = gpsDetails.first;
            transformerType = firstGpsDetail['transformerType'] ?? '';

            // Only consider transformer info if it's not empty
            if (transformerType.isNotEmpty) {
              hasTransformerInfo = true;

              if (transformerType == 'Existing') {
                markerColor = Colors.black;
              } else if (transformerType == 'New') {
                markerColor = AppColors.primaryOrange;
              } else {
                markerColor = isStart ? Colors.red : Colors.green;
              }
            } else {
              // Default color logic if no transformerType
              markerColor = isStart ? Colors.red : Colors.green;
            }
          } else {
            // Default color logic if no gpsDetails
            markerColor = isStart ? Colors.red : Colors.green;
          }
        }

        // Create appropriate marker based on conditions
        final BitmapDescriptor customMarker;
        if (isDoublePoleStructure) {
          // Triangle marker for Double Pole Structure
          customMarker = await createTriangleMarker(markerColor);
        } else if (poleDetails == null || (poleDetails is List && poleDetails.isEmpty)) {
          if (hasTransformerInfo) {
            // Rectangle marker for waypoints with transformer info but no pole details
            customMarker = await createRectangleMarker(markerColor);
          } else {
            // Circle marker for other waypoints
            customMarker = await createDotMarker(markerColor);
          }
        } else {
          // Circle marker for other waypoints
          customMarker = await createDotMarker(markerColor);
        }

        tempMarkers.add(
          Marker(
            markerId: MarkerId('waypoint_$i'),
            position: position,
            icon: customMarker,
            infoWindow: InfoWindow(
              title: wp['name'] ?? 'Waypoint ${i + 1}',
              snippet:
                  wp['isStart'] == true
                      ? 'Start'
                      : wp['isEnd'] == true
                      ? 'End'
                      : 'Waypoint',
            ),
          ),
        );

        // Add each waypoint directly to the polyline points
        tempPolylinePoints.add(position);
      }

      markers.assignAll(tempMarkers);

      if (tempPolylinePoints.isNotEmpty) {
        // Set polyline style based on routeType
        if (routeType == 'New') {
          // New route: Orange solid line
          polylines.assignAll({
            Polyline(
              polylineId: const PolylineId('track_path'),
              points: tempPolylinePoints,
              color: AppColors.primaryOrange,
              width: 4,
            ),
          });
        } else {
          // Existing route: Black dotted line
          polylines.assignAll({
            Polyline(
              polylineId: const PolylineId('track_path'),
              points: tempPolylinePoints,
              color: Colors.black,
              width: 4,
              patterns: [
                PatternItem.dash(10), // 10 pixel dash
                PatternItem.gap(5),   // 5 pixel gap
              ],
            ),
          });
        }
      } else {
        polylines.clear();
      }
      isLoading.value = false;
    } catch (e) {
      if (waypoints.isEmpty) {
        currentLocation.value = newDelhiLocation;

        final customMarker = await createDotMarker(Colors.blue);
        markers.assignAll({
          Marker(
            markerId: const MarkerId('new_delhi'),
            position: newDelhiLocation,
            icon: customMarker,
            infoWindow: const InfoWindow(
              title: 'New Delhi',
              snippet: 'Fallback Location',
            ),
          ),
        });

        polylines.clear();
      }

      isLoading.value = false;
    }
  }
}
