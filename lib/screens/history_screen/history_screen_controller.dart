import 'package:dio/dio.dart';
import 'package:ems/common/constants/general.dart';
import 'package:ems/common/utils/shared_preferences.dart';
import 'package:ems/screens/create_track_screen/create_track_screen.dart';
import 'package:get/get.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:intl/intl.dart';

/// HistoryScreenController manages the route history functionality
///
/// This controller is responsible for:
/// - Fetching and displaying the user's past routes and waypoints
/// - Processing and formatting route data for display
/// - Handling date formatting for route timestamps
/// - Managing navigation to detailed route views
/// - Maintaining loading states during data fetching
///
/// It uses GetX for reactive state management and Dio for API requests.
class HistoryScreenController extends GetxController {
  final RxList<Map<String, dynamic>> routeList = <Map<String, dynamic>>[].obs;
  final RxBool isLoading = false.obs;
  final RxList<List<LatLng>> routeCoordinatesList = RxList<List<LatLng>>();

  /// Fetches the user's route history from the API
  ///
  /// This comprehensive method:
  /// 1. Sets the loading state to true and clears existing data
  /// 2. Retrieves the authentication token for API access
  /// 3. Makes an API request to get all employee waypoints
  /// 4. Processes the response to extract route information
  /// 5. Organizes waypoints into routes (both complete and incomplete)
  /// 6. Ensures each route is unique using timestamp identifiers
  /// 7. Extracts relevant data from each waypoint (coordinates, status, metadata)
  /// 8. Converts waypoint coordinates to LatLng objects for map display
  /// 9. Stores processed routes in routeList and routeCoordinatesList
  /// 10. Sets the loading state to false when complete
  ///
  /// The method handles various edge cases such as empty routes, missing timestamps,
  /// and duplicate routes to ensure a clean and usable history list.
  Future<void> fetchWaypoints() async {
    try {
      isLoading.value = true;
      routeList.clear();
      routeCoordinatesList.clear();

      final authToken = await TokenStorage.getToken();
      final dio = Dio();
      final url = '$baseUrl/api/projects/employee-waypoints';

      final response = await dio.get(
        url,
        options: Options(headers: {'Authorization': 'Bearer $authToken'}),
      );

      if (response.statusCode == 200 && response.data['success'] == true) {
        final List<dynamic> projects = response.data['projects'];
        final Set<String> uniqueRouteIds = {};

        for (var project in projects) {
          if (project['waypoints'] != null) {
            for (var route in project['waypoints']) {
              if (route == null || route.isEmpty) continue;

              final firstWaypointTimestamp = route[0]['timestamp']?.toString();
              if (firstWaypointTimestamp == null) continue;

              if (uniqueRouteIds.contains(firstWaypointTimestamp)) continue;
              uniqueRouteIds.add(firstWaypointTimestamp);

              final filteredRoute =
                  route.map((wp) {
                    // Check if gpsDetails is an array and has at least one element
                    final gpsDetails = wp['gpsDetails'] is List && wp['gpsDetails'].isNotEmpty 
                        ? wp['gpsDetails'][0] 
                        : null;
                    
                    return {
                      'latitude': wp['latitude'],
                      'longitude': wp['longitude'],
                      'isStart': wp['isStart'],
                      'isEnd': wp['isEnd'],
                      'feederName': gpsDetails != null ? gpsDetails['feederName'] ?? 'Unnamed' : 'Unnamed',
                      'timestamp': wp['timestamp'],
                    };
                  }).toList();

              // Process all routes regardless of completion status
              final coords = <LatLng>[];
              for (final wp in filteredRoute) {
                coords.add(
                  LatLng(
                    wp['latitude'].toDouble(),
                    wp['longitude'].toDouble(),
                  ),
                );
              }

              // Use the last waypoint for timestamp and feeder name
              final lastWaypoint = filteredRoute.isNotEmpty ? filteredRoute.last : null;

              if (lastWaypoint != null) {
                routeCoordinatesList.add(coords);
                routeList.add({
                  'projectId': project['projectId'],
                  'waypoints': filteredRoute,
                  'timestamp': lastWaypoint['timestamp'],
                  'feederName': lastWaypoint['feederName'],
                  'coordinates': List<LatLng>.from(coords),
                });
              }
            }
          }
        }
      }
    } finally {
      isLoading.value = false;
    }
  }

  /// Formats the timestamp of a route into a user-friendly date string
  ///
  /// This method:
  /// 1. Validates that the requested index is within the routeList bounds
  /// 2. Retrieves the timestamp from the route at the specified index
  /// 3. Parses the timestamp into a DateTime object
  /// 4. Compares the date with today and yesterday for special formatting
  /// 5. Returns a user-friendly string representation of the date:
  ///    - "Today" for routes created today
  ///    - "Yesterday" for routes created yesterday
  ///    - A formatted date string (e.g., "15 Jan 2023") for older routes
  /// 6. Handles error cases by returning fallback strings
  ///
  /// @param index The index of the route in the routeList
  /// @return A user-friendly formatted date string
  String getFormattedDate(int index) {
    try {
      if (routeList.isEmpty || index >= routeList.length) return 'No date';

      final timestamp = routeList[index]['timestamp'];
      if (timestamp == null) return 'No date';

      final dateTime = DateTime.parse(timestamp);
      final now = DateTime.now();
      final today = DateTime(now.year, now.month, now.day);
      final yesterday = today.subtract(const Duration(days: 1));
      final dateOnly = DateTime(dateTime.year, dateTime.month, dateTime.day);

      if (dateOnly == today) return 'Today';
      if (dateOnly == yesterday) return 'Yesterday';

      return DateFormat('dd MMM yyyy').format(dateTime);
    } catch (e) {
      return 'Invalid date';
    }
  }

  /// Gets the name of a route based on project ID and feeder name
  ///
  /// This method:
  /// 1. Validates that the requested index is within the routeList bounds
  /// 2. Retrieves the projectId and feederName from the route at the specified index
  /// 3. Combines them into a descriptive route name with fallback values if either is missing
  ///
  /// The combined name provides users with a comprehensive identifier for each route,
  /// showing both the project context and the specific feeder involved.
  ///
  /// @param index The index of the route in the routeList
  /// @return A formatted string combining project ID and feeder name
  String getRouteName(int index) {
    if (routeList.isEmpty || index >= routeList.length) return 'Unnamed Route';

    final projectId = routeList[index]['projectId'] ?? 'Unnamed Project';
    final feederName = routeList[index]['feederName'] ?? 'Unnamed Route';

    return '$projectId - $feederName';
  }

  /// Handles the tap event on a route card in the history list
  ///
  /// This method:
  /// 1. Validates that the requested index is within the routeList bounds
  /// 2. Retrieves the route data from the routeList
  /// 3. Navigates to the CreateTrackScreen with the route's waypoints and feeder name
  /// 4. Ensures the waypoints are properly converted to the expected type
  ///
  /// This allows users to view detailed information about a historical route
  /// by tapping on its card in the history list.
  ///
  /// @param index The index of the route in the routeList
  void onRouteCardTap(int index) {
    if (routeList.isEmpty || index >= routeList.length) return;

    final route = routeList[index];

    Get.to(
      () => CreateTrackScreen(),
      arguments: {
        'waypoints': List<Map<String, dynamic>>.from(route['waypoints']),
        'routeName': route['feederName'] ?? 'Unnamed Route',
      },
    );
  }
}
